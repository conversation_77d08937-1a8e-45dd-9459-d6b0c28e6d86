# 最简单的 Dify 部署 - 基于官方镜像
FROM langgenius/dify-api:0.6.16

# 切换到 root 用户进行系统配置
USER root

# 安装 Redis 和 Node.js
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        redis-server \
        curl \
        && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 18
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

# 下载并设置前端
WORKDIR /app
RUN curl -L https://github.com/langgenius/dify/archive/refs/tags/0.6.16.tar.gz | tar -xz --strip-components=1

# 构建前端（使用较小的内存限制）
WORKDIR /app/web
ENV NODE_OPTIONS="--max_old_space_size=768"
ENV NEXT_TELEMETRY_DISABLED=1

RUN npm ci && npm run build

# 创建统一的启动脚本
WORKDIR /app
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 启动 Redis\n\
redis-server --daemonize yes --port 6379 --requirepass "difyai123456"\n\
\n\
# 设置环境变量\n\
export MODE=api\n\
export SECRET_KEY="************************************************"\n\
export APP_WEB_URL="http://localhost:7860"\n\
export CONSOLE_WEB_URL="http://localhost:7860"\n\
export API_URL="http://localhost:7860"\n\
export CONSOLE_API_URL="http://localhost:7860"\n\
export SERVICE_API_URL="http://localhost:7860"\n\
export APP_API_URL="http://localhost:7860"\n\
export REDIS_HOST="localhost"\n\
export REDIS_PORT="6379"\n\
export REDIS_PASSWORD="difyai123456"\n\
export DATABASE_TYPE="sqlite"\n\
export SQLITE_PATH="/app/api/storage/dify.db"\n\
\n\
# 确保存储目录存在\n\
mkdir -p /app/api/storage\n\
\n\
# 初始化数据库\n\
cd /app/api\n\
python -m flask db upgrade\n\
\n\
# 启动 API 服务器\n\
cd /app/api\n\
python -m gunicorn --bind 0.0.0.0:5001 --workers 1 --timeout 200 app:app &\n\
\n\
# 启动前端服务器\n\
cd /app/web\n\
npm start -- --port 7860 &\n\
\n\
# 等待服务启动\n\
wait\n' > /start.sh && chmod +x /start.sh

# 暴露端口
EXPOSE 7860

# 启动
CMD ["/start.sh"]
