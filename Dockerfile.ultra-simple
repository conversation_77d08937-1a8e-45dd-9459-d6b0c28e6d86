# 超简单的 Dify 部署 - 只要能跑起来
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git \
        redis-server \
        curl \
        && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 克隆 Dify 源码（使用较早的稳定版本）
RUN git clone --depth 1 --branch 0.6.10 https://github.com/langgenius/dify.git .

# 安装 Python 依赖
WORKDIR /app/api
RUN pip install --no-cache-dir -r requirements.txt

# 创建简单的前端页面（避免 Node.js 构建）
RUN mkdir -p /app/web/dist && \
    echo '<!DOCTYPE html>
<html>
<head>
    <title>Dify</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; margin-bottom: 30px; }
        .api-list { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .endpoint { margin: 10px 0; padding: 10px; background: white; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Dify API Server</h1>
            <p>Your AI application development platform is running!</p>
        </div>
        <div class="api-list">
            <h3>Available Endpoints:</h3>
            <div class="endpoint"><strong>GET /health</strong> - Health check</div>
            <div class="endpoint"><strong>GET /v1</strong> - API documentation</div>
            <div class="endpoint"><strong>POST /v1/chat-messages</strong> - Chat API</div>
            <div class="endpoint"><strong>POST /v1/completion-messages</strong> - Completion API</div>
        </div>
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="checkHealth()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Check API Health</button>
            <div id="result" style="margin-top: 15px;"></div>
        </div>
    </div>
    <script>
        function checkHealth() {
            fetch("/health")
                .then(response => response.json())
                .then(data => {
                    document.getElementById("result").innerHTML = "<div style=\"background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;\">✅ API is healthy: " + JSON.stringify(data) + "</div>";
                })
                .catch(error => {
                    document.getElementById("result").innerHTML = "<div style=\"background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;\">❌ API not responding</div>";
                });
        }
    </script>
</body>
</html>' > /app/web/dist/index.html

# 创建启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting Dify services..."\n\
\n\
# 启动 Redis\n\
redis-server --daemonize yes --port 6379\n\
\n\
# 设置环境变量\n\
export SECRET_KEY="************************************************"\n\
export DATABASE_TYPE="sqlite"\n\
export SQLITE_PATH="/app/storage/dify.db"\n\
export REDIS_HOST="localhost"\n\
export REDIS_PORT="6379"\n\
\n\
# 创建存储目录\n\
mkdir -p /app/storage\n\
\n\
# 初始化数据库\n\
cd /app/api\n\
echo "Initializing database..."\n\
python -c "from app import create_app; app = create_app(); app.app_context().push(); from extensions.ext_database import db; db.create_all()" || echo "Database already exists"\n\
\n\
# 启动 API 服务器\n\
echo "Starting API server on port 7860..."\n\
cd /app/api\n\
exec python -m gunicorn --bind 0.0.0.0:7860 --workers 1 --timeout 200 app:app\n' > /start.sh && chmod +x /start.sh

# 暴露端口
EXPOSE 7860

# 启动
CMD ["/start.sh"]
