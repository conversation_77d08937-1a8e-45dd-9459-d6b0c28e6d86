# 使用官方 Dify 镜像的最简单方案
FROM ubuntu:22.04

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y \
        docker.io \
        docker-compose \
        curl \
        && rm -rf /var/lib/apt/lists/*

# 下载 Dify 的 docker-compose 配置
WORKDIR /app
RUN curl -O https://raw.githubusercontent.com/langgenius/dify/main/docker/docker-compose.yaml

# 修改配置以适应单容器部署
RUN sed -i 's/127.0.0.1:80:80/0.0.0.0:7860:80/' docker-compose.yaml

# 启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 启动 Docker daemon\n\
dockerd &\n\
sleep 10\n\
\n\
# 启动 Dify\n\
cd /app\n\
docker-compose up\n' > /start.sh && chmod +x /start.sh

EXPOSE 7860

CMD ["/start.sh"]
