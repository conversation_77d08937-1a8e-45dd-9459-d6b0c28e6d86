#!/usr/bin/env bash
set -e

echo "Starting Dify services..."

# 启动 Redis
redis-server --daemonize yes --port 6379 --requirepass "${REDIS_PASSWORD:-difyai123456}"

# 设置默认环境变量（如果 HF Spaces 没有设置的话）
export SECRET_KEY="${SECRET_KEY:-************************************************}"
export DATABASE_TYPE="${DATABASE_TYPE:-sqlite}"
export SQLITE_PATH="${SQLITE_PATH:-/data/dify.sqlite3}"
export REDIS_HOST="${REDIS_HOST:-localhost}"
export REDIS_PORT="${REDIS_PORT:-6379}"
export REDIS_PASSWORD="${REDIS_PASSWORD:-difyai123456}"
export APP_WEB_URL="${APP_WEB_URL:-http://localhost:7860}"
export CONSOLE_WEB_URL="${CONSOLE_WEB_URL:-http://localhost:7860}"
export API_URL="${API_URL:-http://localhost:7860}"
export CONSOLE_API_URL="${CONSOLE_API_URL:-http://localhost:7860}"
export SERVICE_API_URL="${SERVICE_API_URL:-http://localhost:7860}"
export APP_API_URL="${APP_API_URL:-http://localhost:7860}"

# 创建数据目录
mkdir -p "$(dirname "$SQLITE_PATH")"

# 初始化数据库
cd /app/api
echo "Initializing database..."
flask db upgrade

# 启动后端 API
echo "Starting API server..."
gunicorn app:app \
    --bind 0.0.0.0:5001 \
    --workers 1 \
    --worker-class gevent \
    --timeout 200 &

# 启动前端
echo "Starting web server..."
cd /app/web
PORT=7860 npm start &

# 等待服务启动
wait
