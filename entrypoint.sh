#!/usr/bin/env bash
set -e

# 1) 自动起一个本地 Redis，避免再开容器
redis-server --daemonize yes --port 6379 --requirepass "${REDIS_PASSWORD:-difypass}"

# 2) 初始化 SQLite（也可以连外部 Postgres；改 env 即可）
export DATABASE_TYPE=${DATABASE_TYPE:-sqlite}
export SQLITE_PATH=${SQLITE_PATH:-/data/dify.sqlite3}
mkdir -p "$(dirname "$SQLITE_PATH")" && touch "$SQLITE_PATH"

# 3) 数据库迁移
cd /app/api
FLASK_APP=app.py flask db upgrade

# 4) 启动后端 API（改到 7860 端口）
cd /app/api
exec gunicorn app:app \
        --bind 0.0.0.0:7860 \
        --workers 1 \
        --worker-class gevent \
        --timeout 120
