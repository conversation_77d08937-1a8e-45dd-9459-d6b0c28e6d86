#!/usr/bin/env bash
set -e

echo "Starting Dify 1.7.1 services..."

# 启动 Redis
redis-server --daemonize yes --port 6379 --requirepass "${REDIS_PASSWORD:-difyai123456}"

# 设置 1.7.x 版本的环境变量（如果 HF Spaces 没有设置的话）
export SECRET_KEY="${SECRET_KEY:-************************************************}"
export DATABASE_TYPE="${DATABASE_TYPE:-sqlite}"
export SQLITE_PATH="${SQLITE_PATH:-/data/dify.sqlite3}"
export REDIS_HOST="${REDIS_HOST:-localhost}"
export REDIS_PORT="${REDIS_PORT:-6379}"
export REDIS_PASSWORD="${REDIS_PASSWORD:-difyai123456}"

# 1.7.x 版本的 URL 配置
export APP_WEB_URL="${APP_WEB_URL:-http://localhost:7860}"
export CONSOLE_WEB_URL="${CONSOLE_WEB_URL:-http://localhost:7860}"
export API_URL="${API_URL:-http://localhost:7860}"
export CONSOLE_API_URL="${CONSOLE_API_URL:-http://localhost:7860}"
export SERVICE_API_URL="${SERVICE_API_URL:-http://localhost:7860}"
export APP_API_URL="${APP_API_URL:-http://localhost:7860}"

# 1.7.x 版本新增的环境变量
export MODE="${MODE:-api}"
export LOG_LEVEL="${LOG_LEVEL:-INFO}"
export MIGRATION_ENABLED="${MIGRATION_ENABLED:-true}"

# 创建数据目录
mkdir -p "$(dirname "$SQLITE_PATH")"

# 初始化数据库 - 1.7.x 版本
cd /app/api
echo "Initializing database for Dify 1.7.1..."

# 设置 Flask 应用
export FLASK_APP=app.py

# 运行数据库迁移
if [ "$MIGRATION_ENABLED" = "true" ]; then
    echo "Running database migrations..."
    flask db upgrade
fi

# 启动后端 API - 1.7.x 版本优化
echo "Starting API server..."
gunicorn app:app \
    --bind 0.0.0.0:5001 \
    --workers 1 \
    --worker-class gevent \
    --timeout 300 \
    --keep-alive 2 \
    --max-requests 1000 \
    --max-requests-jitter 50 &

# 启动前端 - 1.7.x 版本
echo "Starting web server..."
cd /app/web
PORT=7860 pnpm start &

# 等待服务启动
echo "Services started. Waiting for processes..."
wait
