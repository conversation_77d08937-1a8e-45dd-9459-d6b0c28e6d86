##############################
#  Hugging Face Spaces – 使用预构建前端的 Dify
##############################
FROM python:3.10-slim

#####################
# 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8

WORKDIR /app

#####################
# 系统依赖
#####################
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl build-essential \
        redis-server \
        libxml2-dev libxslt1-dev zlib1g-dev && \
    rm -rf /var/lib/apt/lists/*

# 安装 Node.js (仅用于运行时)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

#####################
# 安装 uv
#####################
RUN pip install --no-cache-dir "uv>=0.6"

#####################
# 拉取 Dify 源码
#####################
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

#####################
# 安装后端依赖
#####################
WORKDIR /opt/dify/api
RUN uv export --format requirements-txt --no-hashes > /tmp/req.txt && \
    pip install --no-cache-dir -r /tmp/req.txt && \
    rm -f /tmp/req.txt

#####################
# 下载预构建的前端 (如果可用)
#####################
# 这里我们创建一个轻量级的前端替代
RUN mkdir -p /app/web && \
    echo '<!DOCTYPE html>
<html>
<head>
    <title>Dify</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .logo { font-size: 2.5em; font-weight: bold; color: #1f2937; margin-bottom: 10px; }
        .subtitle { color: #6b7280; font-size: 1.1em; }
        .api-section { margin: 30px 0; }
        .endpoint { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 15px; margin: 10px 0; }
        .method { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; margin-right: 10px; }
        .get { background: #10b981; color: white; }
        .post { background: #3b82f6; color: white; }
        .status { margin-top: 30px; padding: 20px; background: #ecfdf5; border: 1px solid #10b981; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖 Dify</div>
            <div class="subtitle">AI Application Development Platform</div>
        </div>
        
        <div class="api-section">
            <h2>API Endpoints</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/health</strong> - Health check
            </div>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/v1</strong> - API documentation
            </div>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/v1/chat-messages</strong> - Chat completion
            </div>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/v1/completion-messages</strong> - Text completion
            </div>
        </div>
        
        <div id="status" class="status">
            <strong>🔄 Checking API status...</strong>
        </div>
    </div>
    
    <script>
        fetch("/health")
            .then(response => response.json())
            .then(data => {
                document.getElementById("status").innerHTML = "<strong>✅ API is healthy</strong><br><pre>" + JSON.stringify(data, null, 2) + "</pre>";
            })
            .catch(error => {
                document.getElementById("status").innerHTML = "<strong>⚠️ API is starting up...</strong><br>Please wait a moment and refresh the page.";
            });
    </script>
</body>
</html>' > /app/web/index.html

#####################
# 复制后端代码
#####################
RUN cp -r /opt/dify/api /app/api && \
    rm -rf /opt/dify

#####################
# 启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 暴露端口
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
