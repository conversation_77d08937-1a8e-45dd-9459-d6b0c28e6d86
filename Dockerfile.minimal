##############################
#  Hugging Face Spaces – 最小化内存版本 Dify
##############################
FROM python:3.10-slim

#####################
# 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8 \
    PYTHONDONTWRITEBYTECODE=1

WORKDIR /app

#####################
# 最小化系统依赖安装
#####################
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl redis-server && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

#####################
# 安装轻量级 Node.js
#####################
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g pnpm && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

#####################
# 拉取 Dify 源码 (浅克隆)
#####################
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --single-branch --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /tmp/dify

#####################
# 安装最小化后端依赖
#####################
WORKDIR /tmp/dify/api

# 创建最小化的 requirements.txt
RUN echo "flask==2.3.3" > /tmp/minimal_requirements.txt && \
    echo "gunicorn==21.2.0" >> /tmp/minimal_requirements.txt && \
    echo "gevent==23.7.0" >> /tmp/minimal_requirements.txt && \
    echo "redis==4.6.0" >> /tmp/minimal_requirements.txt && \
    echo "sqlalchemy==2.0.21" >> /tmp/minimal_requirements.txt && \
    echo "flask-sqlalchemy==3.0.5" >> /tmp/minimal_requirements.txt && \
    echo "flask-migrate==4.0.5" >> /tmp/minimal_requirements.txt && \
    echo "python-dotenv==1.0.0" >> /tmp/minimal_requirements.txt

RUN pip install --no-cache-dir -r /tmp/minimal_requirements.txt && \
    rm -f /tmp/minimal_requirements.txt

#####################
# 跳过前端构建，使用预构建版本或简化版本
#####################
# 创建一个简单的静态前端
RUN mkdir -p /app/web && \
    echo '<!DOCTYPE html><html><head><title>Dify</title></head><body><h1>Dify is starting...</h1><p>Please wait while the application loads.</p></body></html>' > /app/web/index.html

#####################
# 复制必要的后端文件
#####################
RUN cp -r /tmp/dify/api /app/api && \
    # 清理不必要的文件
    find /app/api -name "*.pyc" -delete && \
    find /app/api -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true && \
    rm -rf /tmp/dify

#####################
# 创建简化的启动脚本
#####################
RUN echo '#!/bin/bash\n\
set -e\n\
redis-server --daemonize yes --port 6379 --requirepass "${REDIS_PASSWORD:-difypass}"\n\
export DATABASE_TYPE=${DATABASE_TYPE:-sqlite}\n\
export SQLITE_PATH=${SQLITE_PATH:-/data/dify.sqlite3}\n\
mkdir -p "$(dirname "$SQLITE_PATH")" && touch "$SQLITE_PATH"\n\
cd /app/api\n\
python -c "from app import create_app; app = create_app(); app.app_context().push(); from extensions.ext_database import db; db.create_all()" || true\n\
exec gunicorn app:app --bind 0.0.0.0:7860 --workers 1 --worker-class gevent\n' > /entrypoint.sh && \
    chmod +x /entrypoint.sh

#####################
# 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
