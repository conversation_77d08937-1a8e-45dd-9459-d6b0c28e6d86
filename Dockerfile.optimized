##############################
#  Hugging Face Spaces – 单容器版 Dify (内存优化版)
##############################
FROM python:3.10-slim

#####################
# 0. 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8

WORKDIR /app

#####################
# 1. 系统依赖 & Node 22 (分步安装减少内存峰值)
#####################
# 1) 先装基础工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl && \
    rm -rf /var/lib/apt/lists/*

# 2) 安装编译依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        libxml2-dev libxslt1-dev zlib1g-dev && \
    rm -rf /var/lib/apt/lists/*

# 3) 安装 Redis
RUN apt-get update && \
    apt-get install -y --no-install-recommends redis-server && \
    rm -rf /var/lib/apt/lists/*

# 4) 安装 Node 22（降低内存使用）
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g pnpm && \
    rm -rf /var/lib/apt/lists/*

#####################
# 2. 安装 uv（后端锁依赖）
#####################
RUN pip install --no-cache-dir "uv>=0.6"

#####################
# 3. 拉取指定版本 Dify 源码
#####################
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

#####################
# 4. 安装后端依赖（按 uv.lock）
#####################
WORKDIR /opt/dify/api
RUN uv export --format requirements-txt --no-hashes > /tmp/req.txt && \
    pip install --no-cache-dir -r /tmp/req.txt && \
    rm -f /tmp/req.txt

#####################
# 5. 构建前端（Next.js）- 内存优化
#####################
WORKDIR /opt/dify/web
ENV NEXT_TELEMETRY_DISABLED=1 \
    NODE_OPTIONS="--max_old_space_size=1024" \
    NODE_ENV=production

# 分步安装和构建，减少内存峰值
RUN pnpm install --ignore-scripts --frozen-lockfile && \
    pnpm run build && \
    # 清理 node_modules 释放内存
    rm -rf node_modules

# 把产物搬到 /app/web，保持单容器
RUN mkdir -p /app/web && \
    cp -r .next/standalone /app/web && \
    cp -r public /app/web/.next/standalone && \
    # 清理构建临时文件
    rm -rf /opt/dify/web/.next

#####################
# 6. 拷贝 API 代码到 /app
#####################
RUN cp -r /opt/dify/api /app/api && \
    # 清理源码目录释放空间
    rm -rf /opt/dify

#####################
# 7. 启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 8. 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
