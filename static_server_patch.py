#!/usr/bin/env python3
"""
为 Dify API 添加静态文件服务的补丁
"""

import os
import sys

def patch_app_py():
    """修改 app.py 文件，添加静态文件路由"""
    
    app_py_path = "/app/api/app.py"
    
    if not os.path.exists(app_py_path):
        print(f"Warning: {app_py_path} not found")
        return
    
    # 读取原文件
    with open(app_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加静态文件服务代码
    static_route_code = '''
# 添加静态文件服务
from flask import send_from_directory, send_file

@app.route('/')
def serve_index():
    """服务主页"""
    static_dir = '/app/web'
    index_path = os.path.join(static_dir, 'index.html')
    if os.path.exists(index_path):
        return send_file(index_path)
    return {"message": "Dify API Server is running", "status": "ok"}

@app.route('/health')
def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "dify-api"}

'''
    
    # 在文件末尾添加静态路由
    if 'serve_index' not in content:
        content += static_route_code
    
    # 写回文件
    with open(app_py_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Successfully patched app.py with static file serving")

if __name__ == "__main__":
    patch_app_py()
