##############################
#  Hugging Face Spaces – 单容器版 Dify (多阶段构建优化版)
##############################

# 阶段1: 构建前端
FROM node:22-slim AS frontend-builder

WORKDIR /build

# 安装基础工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends git && \
    rm -rf /var/lib/apt/lists/*

# 拉取 Dify 源码
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /build/dify

# 构建前端 (内存限制)
WORKDIR /build/dify/web
ENV NEXT_TELEMETRY_DISABLED=1 \
    NODE_OPTIONS="--max_old_space_size=512" \
    NODE_ENV=production

RUN npm install -g pnpm && \
    pnpm install --ignore-scripts --frozen-lockfile && \
    pnpm run build

# 阶段2: 准备后端依赖
FROM python:3.10-slim AS backend-builder

WORKDIR /build

# 安装基础工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git build-essential \
        libxml2-dev libxslt1-dev zlib1g-dev && \
    rm -rf /var/lib/apt/lists/*

# 安装 uv
RUN pip install --no-cache-dir "uv>=0.6"

# 拉取 Dify 源码
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /build/dify

# 导出后端依赖
WORKDIR /build/dify/api
RUN uv export --format requirements-txt --no-hashes > /build/requirements.txt

# 阶段3: 最终运行时镜像
FROM python:3.10-slim AS runtime

#####################
# 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8

WORKDIR /app

#####################
# 安装运行时依赖
#####################
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        redis-server \
        libxml2 libxslt1.1 && \
    rm -rf /var/lib/apt/lists/*

# 安装 Node.js (运行时版本)
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

#####################
# 复制和安装后端依赖
#####################
COPY --from=backend-builder /build/requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir -r /tmp/requirements.txt && \
    rm -f /tmp/requirements.txt

#####################
# 复制应用代码
#####################
# 复制后端代码
COPY --from=backend-builder /build/dify/api /app/api

# 复制前端构建产物
COPY --from=frontend-builder /build/dify/web/.next/standalone /app/web
COPY --from=frontend-builder /build/dify/web/public /app/web/public

#####################
# 启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
