##############################
#  Hugging Face Spaces – 单容器版 Dify (内存优化版)
##############################
FROM python:3.10-slim

#####################
# 0. 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8

WORKDIR /app

#####################
# 1. 系统依赖 & Node 22 (分步安装减少内存峰值)
#####################
# 1) 先装基础工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl && \
    rm -rf /var/lib/apt/lists/*

# 2) 安装编译依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        libxml2-dev libxslt1-dev zlib1g-dev && \
    rm -rf /var/lib/apt/lists/*

# 3) 安装 Redis
RUN apt-get update && \
    apt-get install -y --no-install-recommends redis-server && \
    rm -rf /var/lib/apt/lists/*

# 4) 安装 Node 22（降低内存使用）
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g pnpm && \
    rm -rf /var/lib/apt/lists/*

#####################
# 2. 安装 uv（后端锁依赖）
#####################
RUN pip install --no-cache-dir "uv>=0.6"

#####################
# 3. 拉取指定版本 Dify 源码
#####################
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

#####################
# 4. 安装后端依赖（按 uv.lock）
#####################
WORKDIR /opt/dify/api
RUN uv export --format requirements-txt --no-hashes > /tmp/req.txt && \
    pip install --no-cache-dir -r /tmp/req.txt && \
    rm -f /tmp/req.txt

#####################
# 5. 跳过前端构建，创建简单的静态页面
#####################
# 创建一个简单的前端页面，直接访问 API
RUN mkdir -p /app/web && \
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify API Server</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .api-info { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .endpoint { background: #e8f4fd; padding: 10px; margin: 10px 0; border-radius: 4px; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🚀 Dify API Server</h1>
    <p>Dify 后端 API 服务已启动！</p>

    <div class="api-info">
        <h2>API 端点</h2>
        <div class="endpoint">
            <strong>健康检查:</strong> <code>GET /health</code>
        </div>
        <div class="endpoint">
            <strong>API 文档:</strong> <code>GET /v1</code>
        </div>
        <div class="endpoint">
            <strong>控制台 API:</strong> <code>/console/api</code>
        </div>
    </div>

    <div class="api-info">
        <h2>使用说明</h2>
        <p>1. 这是一个仅包含后端 API 的 Dify 部署</p>
        <p>2. 您可以通过 API 调用来使用 Dify 的功能</p>
        <p>3. 如需完整的 Web 界面，请在本地环境中构建前端</p>
    </div>

    <script>
        // 简单的 API 状态检查
        fetch("/health")
            .then(response => response.json())
            .then(data => {
                document.body.innerHTML += "<div class=\"api-info\"><h2>✅ API 状态</h2><pre>" + JSON.stringify(data, null, 2) + "</pre></div>";
            })
            .catch(error => {
                document.body.innerHTML += "<div class=\"api-info\"><h2>❌ API 状态</h2><p>API 服务可能还在启动中...</p></div>";
            });
    </script>
</body>
</html>' > /app/web/index.html

#####################
# 6. 拷贝 API 代码到 /app 并应用补丁
#####################
RUN cp -r /opt/dify/api /app/api && \
    # 清理源码目录释放空间
    rm -rf /opt/dify

# 应用静态文件服务补丁
COPY static_server_patch.py /tmp/patch.py
RUN python /tmp/patch.py && rm -f /tmp/patch.py

#####################
# 7. 启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 8. 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
