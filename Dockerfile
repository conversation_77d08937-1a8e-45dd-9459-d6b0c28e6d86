##############################
#  Hugging Face Spaces – Dify 单容器部署
##############################

# 多阶段构建：第一阶段构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /build

# 安装 git
RUN apk add --no-cache git

# 克隆 Dify 源码
ARG DIFY_TAG=0.6.16
RUN git clone --depth 1 --branch ${DIFY_TAG} https://github.com/langgenius/dify.git .

# 构建前端 - 使用极小的内存设置
WORKDIR /build/web
ENV NODE_OPTIONS="--max_old_space_size=512"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# 使用 npm 而不是 pnpm，减少内存使用
RUN npm ci --only=production --no-audit --no-fund && \
    npm run build && \
    # 立即清理 node_modules 释放内存
    rm -rf node_modules

# 第二阶段：运行时环境
FROM python:3.10-slim

#####################
# 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8

WORKDIR /app

#####################
# 安装系统依赖
#####################
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl \
        redis-server \
        libxml2-dev libxslt1-dev zlib1g-dev \
        build-essential && \
    rm -rf /var/lib/apt/lists/*

# 安装 Node.js 18 (运行时)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

#####################
# 克隆后端代码
#####################
ARG DIFY_TAG=0.6.16
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

#####################
# 安装后端依赖
#####################
WORKDIR /opt/dify/api
RUN pip install --no-cache-dir -r requirements.txt

#####################
# 复制前端构建产物
#####################
COPY --from=frontend-builder /build/web/.next /app/web/.next
COPY --from=frontend-builder /build/web/public /app/web/public
COPY --from=frontend-builder /build/web/package.json /app/web/package.json

# 安装前端运行时依赖
WORKDIR /app/web
RUN npm install --only=production --no-audit --no-fund

#####################
# 复制后端代码
#####################
RUN cp -r /opt/dify/api /app/api && \
    rm -rf /opt/dify

#####################
# 复制启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
