##############################
#  Hugging Face Spaces – 单容器版 Dify
##############################
FROM python:3.10-slim

#####################
# 0. 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8

WORKDIR /app

#####################
# 1. 系统依赖 & Node 22
#####################
# 1) 先装常用工具和编译依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl build-essential \
        redis-server \
        libxml2-dev libxslt1-dev zlib1g-dev

# 2) 安装 Node 22（dify-web 需要）
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g pnpm && \
    rm -rf /var/lib/apt/lists/*

#####################
# 2. 安装 uv（后端锁依赖）
#####################
RUN pip install --no-cache-dir "uv>=0.6"

#####################
# 3. 拉取指定版本 Dify 源码
#####################
ARG DIFY_TAG=1.7.0
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

#####################
# 4. 安装后端依赖（按 uv.lock）
#####################
WORKDIR /opt/dify/api
RUN uv export --format requirements-txt --no-hashes > /tmp/req.txt && \
    pip install --no-cache-dir -r /tmp/req.txt

#####################
# 5. 构建前端（Next.js）
#####################
WORKDIR /opt/dify/web
ENV NEXT_TELEMETRY_DISABLED=1 \
    NODE_OPTIONS="--max_old_space_size=2560"
RUN pnpm install --ignore-scripts && \
    pnpm run build

# 把产物搬到 /app/web，保持单容器
RUN mkdir -p /app/web && \
    cp -r .next/standalone /app/web && \
    cp -r public /app/web/.next/standalone

#####################
# 6. 拷贝 API 代码到 /app
#####################
RUN cp -r /opt/dify/api /app/api

#####################
# 7. 启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 8. 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
