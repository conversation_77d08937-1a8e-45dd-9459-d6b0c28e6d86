##############################
#  Hugging Face Spaces – Dify 单容器部署
##############################

# 多阶段构建：第一阶段构建前端
FROM node:20-alpine AS frontend-builder

WORKDIR /build

# 安装 git 和 pnpm
RUN apk add --no-cache git && \
    npm install -g pnpm@latest

# 克隆 Dify 源码
ARG DIFY_TAG=1.7.1
RUN git clone --depth 1 --branch ${DIFY_TAG} https://github.com/langgenius/dify.git .

# 构建前端 - 针对 1.7.x 版本优化内存设置
WORKDIR /build/web
ENV NODE_OPTIONS="--max_old_space_size=768"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# 使用 pnpm 安装和构建 - 1.7.x 版本优化
RUN pnpm install --frozen-lockfile && \
    pnpm run build && \
    # 立即清理 node_modules 释放内存
    rm -rf node_modules

# 第二阶段：运行时环境
FROM python:3.11-slim

#####################
# 基础环境变量
#####################
ENV PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    LANG=C.UTF-8 \
    PYTHONDONTWRITEBYTECODE=1

WORKDIR /app

#####################
# 安装系统依赖 - 1.7.x 版本要求
#####################
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git curl \
        redis-server \
        libxml2-dev libxslt1-dev zlib1g-dev \
        libffi-dev libssl-dev \
        build-essential && \
    rm -rf /var/lib/apt/lists/*

# 安装 Node.js 20 (运行时) - 匹配构建环境
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

#####################
# 克隆后端代码
#####################
ARG DIFY_TAG=1.7.1
RUN git clone --depth 1 --branch ${DIFY_TAG} \
    https://github.com/langgenius/dify.git /opt/dify

#####################
# 安装后端依赖 - 1.7.x 版本优化
#####################
WORKDIR /opt/dify/api

# 安装 uv 用于更快的依赖管理
RUN pip install --no-cache-dir uv

# 使用 uv 安装依赖（如果有 uv.lock）或回退到 pip
RUN if [ -f "uv.lock" ]; then \
        uv export --format requirements-txt --no-hashes > /tmp/requirements.txt && \
        pip install --no-cache-dir -r /tmp/requirements.txt && \
        rm /tmp/requirements.txt; \
    else \
        pip install --no-cache-dir -r requirements.txt; \
    fi

#####################
# 复制前端构建产物
#####################
COPY --from=frontend-builder /build/web/.next /app/web/.next
COPY --from=frontend-builder /build/web/public /app/web/public
COPY --from=frontend-builder /build/web/package.json /app/web/package.json
COPY --from=frontend-builder /build/web/pnpm-lock.yaml /app/web/pnpm-lock.yaml

# 安装前端运行时依赖
WORKDIR /app/web
RUN npm install -g pnpm && \
    pnpm install --prod --frozen-lockfile

#####################
# 复制后端代码
#####################
RUN cp -r /opt/dify/api /app/api && \
    rm -rf /opt/dify

#####################
# 复制启动脚本
#####################
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#####################
# 暴露端口 & 启动
#####################
EXPOSE 7860
CMD ["/entrypoint.sh"]
