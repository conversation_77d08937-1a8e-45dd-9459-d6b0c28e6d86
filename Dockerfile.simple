# 简单直接的 Dify 部署方案 - 适用于 Hugging Face Spaces
FROM node:18-slim AS frontend

# 设置工作目录
WORKDIR /app

# 安装 git
RUN apt-get update && apt-get install -y git && rm -rf /var/lib/apt/lists/*

# 克隆 Dify 源码
ARG DIFY_VERSION=0.6.16
RUN git clone --depth 1 --branch ${DIFY_VERSION} https://github.com/langgenius/dify.git .

# 构建前端 - 使用更保守的内存设置
WORKDIR /app/web
ENV NODE_OPTIONS="--max_old_space_size=512"
ENV NEXT_TELEMETRY_DISABLED=1

RUN npm ci --only=production && npm run build

# 第二阶段：Python 后端
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        git \
        redis-server \
        && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 克隆 Dify 源码
ARG DIFY_VERSION=0.6.16
RUN git clone --depth 1 --branch ${DIFY_VERSION} https://github.com/langgenius/dify.git .

# 安装 Python 依赖
WORKDIR /app/api
RUN pip install --no-cache-dir -r requirements.txt

# 从前端构建阶段复制构建产物
COPY --from=frontend /app/web/.next /app/web/.next
COPY --from=frontend /app/web/public /app/web/public
COPY --from=frontend /app/web/package.json /app/web/package.json

# 安装 Node.js 运行时
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

# 创建启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 启动 Redis\n\
redis-server --daemonize yes --port 6379\n\
\n\
# 设置环境变量\n\
export SECRET_KEY=${SECRET_KEY:-"************************************************"}\n\
export DATABASE_TYPE=${DATABASE_TYPE:-sqlite}\n\
export SQLITE_PATH=${SQLITE_PATH:-/tmp/dify.db}\n\
\n\
# 初始化数据库\n\
cd /app/api\n\
flask db upgrade\n\
\n\
# 启动后端\n\
gunicorn --bind 0.0.0.0:7860 --workers 1 --timeout 200 app:app &\n\
\n\
# 启动前端\n\
cd /app/web\n\
npm start &\n\
\n\
wait\n' > /start.sh && chmod +x /start.sh

# 暴露端口
EXPOSE 7860

# 启动
CMD ["/start.sh"]
